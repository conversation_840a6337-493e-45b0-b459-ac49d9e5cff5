import axios from 'axios';
import FormData from 'form-data';
import OpenAI from 'openai';
import type { FortnoxUploadAndCreateVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
  openai?: OpenAI;
}

/**
 * Fortnox Archive API response interface
 */
interface FortnoxArchiveResponse {
  Archive: {
    Id: string;
    Name: string;
    Size: number;
    Path: string;
  };
}

/**
 * Fortnox Voucher Row interface
 */
interface FortnoxVoucherRow {
  Account: string;
  Debit?: number;
  Credit?: number;
  Description?: string;
}

/**
 * Fortnox Voucher interface
 */
interface FortnoxVoucher {
  Description: string;
  TransactionDate: string;
  VoucherSeries: string;
  VoucherRows: FortnoxVoucherRow[];
}

/**
 * AI response interface for voucher rows
 */
interface AIVoucherRowsResponse {
  rows: Array<{
    account: string;
    debit?: number;
    credit?: number;
    description?: string;
  }>;
  transactionDate?: string;
  explanation?: string;
}

/**
 * Execute Fortnox Upload and Create Voucher step
 */
export async function executeFortnoxUploadAndCreateVoucher(
  step: FortnoxUploadAndCreateVoucherStep,
  context: FortnoxExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId, openai } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Upload and Create Voucher: ${step.id}`,
      stepId: step.id
    });

    // Check if OpenAI is available for AI processing
    if (!openai) {
      throw new Error('OpenAI client is required for AI-powered voucher creation');
    }

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    // STEP 1: Upload file to Fortnox Archive
    onLog({
      level: 'info',
      message: 'Step 1: Uploading file to Fortnox Archive',
      stepId: step.id
    });

    // Get base64 file content from variable
    const interpolatedFileInputVariable = interpolateVariables(step.fileInputVariable, variables);
    const base64Content = variables[interpolatedFileInputVariable];

    if (!base64Content) {
      throw new Error(`No file content found in variable: ${interpolatedFileInputVariable}`);
    }

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = base64Content;
    if (typeof base64Data === 'string' && base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Convert base64 to buffer
    let fileBuffer: Buffer;
    try {
      fileBuffer = Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      throw new Error(`Invalid base64 data in variable ${interpolatedFileInputVariable}: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    // Determine filename
    let filename: string = step.filename || '';
    if (!filename) {
      // Try to get filename from related variable
      const filenameVariable = `${interpolatedFileInputVariable}_filename`;
      filename = variables[filenameVariable] || 'uploaded_file.bin';
    }
    filename = interpolateVariables(filename, variables);

    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: filename,
      contentType: 'application/octet-stream'
    });

    // Add file description if provided
    if (step.fileDescription) {
      const interpolatedFileDescription = interpolateVariables(step.fileDescription, variables);
      formData.append('description', interpolatedFileDescription);
    }

    // Upload file to Fortnox Archive
    let uploadResponse;
    try {
      uploadResponse = await axios.post(
        'https://api.fortnox.se/3/archive',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Accept': 'application/json',
            ...formData.getHeaders()
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );
    } catch (apiError: any) {
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const uploadedFile = uploadResponse.data as FortnoxArchiveResponse;
    const fileId = uploadedFile.Archive.Id;

    onLog({
      level: 'info',
      message: `File uploaded successfully: ${uploadedFile.Archive.Name} (ID: ${fileId})`,
      stepId: step.id
    });

    // STEP 2: Create voucher with AI processing
    onLog({
      level: 'info',
      message: 'Step 2: Creating voucher with AI processing',
      stepId: step.id
    });

    // Get voucher input data
    const interpolatedVoucherInputVariable = interpolateVariables(step.voucherInputVariable, variables);
    const inputData = variables[interpolatedVoucherInputVariable];

    if (!inputData) {
      throw new Error(`No input data found in variable: ${interpolatedVoucherInputVariable}`);
    }

    // AI system prompt for voucher creation
    const systemPrompt = `Du är en expert på svensk bokföring och Fortnox API. Din uppgift är att skapa verifikationsrader baserat på input-data.

VIKTIGA REGLER:
1. Verifikationen MÅSTE vara balanserad (totalt debet = totalt kredit)
2. Använd endast giltiga kontonummer enligt svensk kontoplan (BAS-kontoplanen)
3. Alla belopp måste vara positiva tal
4. Om input-data innehåller ett datum, använd det som transactionDate
5. Svara ENDAST med JSON i följande format:

{
  "rows": [
    {
      "account": "kontonummer",
      "debit": belopp_eller_null,
      "credit": belopp_eller_null,
      "description": "beskrivning"
    }
  ],
  "transactionDate": "YYYY-MM-DD (om datum finns i input-data)",
  "explanation": "kort förklaring av verifikationen"
}

INSTRUKTIONER:
Användaren kommer att ange vilka konton som ska användas i sin prompt. Följ användarens instruktioner för kontohantering.`;

    // Interpolate the AI prompt with variables
    const interpolatedPrompt = interpolateVariables(step.aiPrompt, variables);

    // Prepare input data as string
    const inputDataStr = typeof inputData === 'string' ? inputData : JSON.stringify(inputData, null, 2);

    // Send to AI for processing
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `Skapa verifikationsrader för följande data:

INPUT DATA:
${inputDataStr}

INSTRUKTIONER:
${interpolatedPrompt}

Svara endast med JSON enligt formatet ovan.`
        }
      ],
      temperature: 0.1
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw new Error('No response from AI');
    }

    // Parse AI response
    let aiVoucherData: AIVoucherRowsResponse;
    try {
      // Extract JSON from response (in case AI adds extra text)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : aiResponse;
      aiVoucherData = JSON.parse(jsonStr);
    } catch (parseError) {
      throw new Error(`Failed to parse AI response as JSON: ${parseError instanceof Error ? parseError.message : 'Unknown error'}. Response: ${aiResponse}`);
    }

    if (!aiVoucherData.rows || !Array.isArray(aiVoucherData.rows) || aiVoucherData.rows.length === 0) {
      throw new Error('AI response must contain at least one voucher row');
    }

    // Validate and convert AI rows to Fortnox format
    const voucherRows: FortnoxVoucherRow[] = [];
    let totalDebit = 0;
    let totalCredit = 0;

    for (const aiRow of aiVoucherData.rows) {
      // Validate amounts
      if (aiRow.debit && aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} cannot have both debit and credit`);
      }

      if (!aiRow.debit && !aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} must have either debit or credit`);
      }

      // Validate account number format (basic validation)
      if (!aiRow.account || !/^\d+$/.test(aiRow.account)) {
        throw new Error(`Invalid account number format: ${aiRow.account}. Must be numeric.`);
      }

      const voucherRow: FortnoxVoucherRow = {
        Account: aiRow.account,
        Description: aiRow.description || interpolateVariables(step.voucherDescription || '', variables)
      };

      if (aiRow.debit) {
        voucherRow.Debit = aiRow.debit;
        totalDebit += aiRow.debit;
      }

      if (aiRow.credit) {
        voucherRow.Credit = aiRow.credit;
        totalCredit += aiRow.credit;
      }

      voucherRows.push(voucherRow);
    }

    // Validate that voucher is balanced
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      throw new Error(`Voucher is not balanced. Total debit: ${totalDebit}, Total credit: ${totalCredit}`);
    }

    // Determine transaction date
    let transactionDate = step.transactionDate;
    if (aiVoucherData.transactionDate) {
      transactionDate = aiVoucherData.transactionDate;
    }
    if (!transactionDate) {
      transactionDate = new Date().toISOString().split('T')[0]; // Today's date
    }
    transactionDate = interpolateVariables(transactionDate, variables);

    // Create voucher
    const voucher: FortnoxVoucher = {
      Description: interpolateVariables(step.voucherDescription || 'AI Generated Voucher with File', variables),
      TransactionDate: transactionDate,
      VoucherSeries: step.voucherSeries || 'A',
      VoucherRows: voucherRows
    };

    // Send voucher to Fortnox
    let voucherResponse;
    try {
      voucherResponse = await axios.post(
        'https://api.fortnox.se/3/vouchers',
        { Voucher: voucher },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Voucher API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Voucher API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const createdVoucher = voucherResponse.data.Voucher;

    onLog({
      level: 'info',
      message: `Voucher created successfully: ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber}`,
      stepId: step.id
    });

    // STEP 3: Attach file to voucher
    onLog({
      level: 'info',
      message: 'Step 3: Attaching file to voucher',
      stepId: step.id
    });

    try {
      const voucherFileConnection = {
        FileId: fileId,
        VoucherNumber: createdVoucher.VoucherNumber,
        VoucherSeries: createdVoucher.VoucherSeries
      };

      await axios.post(
        'https://api.fortnox.se/3/voucherfileconnections',
        { VoucherFileConnection: voucherFileConnection },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );

      onLog({
        level: 'info',
        message: `File ${fileId} attached to voucher successfully`,
        stepId: step.id
      });

    } catch (attachError: any) {
      onLog({
        level: 'warn',
        message: `Failed to attach file to voucher: ${attachError.message}`,
        stepId: step.id
      });
    }

    // Store combined result in variables
    const variableName = step.variableName || getDefaultVariableName('fortnoxUploadAndCreateVoucher', stepIndex);
    const combinedResult = {
      // File information
      fileId: fileId,
      filename: uploadedFile.Archive.Name,
      fileSize: uploadedFile.Archive.Size,
      filePath: uploadedFile.Archive.Path,
      // Voucher information
      voucherNumber: createdVoucher.VoucherNumber,
      voucherSeries: createdVoucher.VoucherSeries,
      voucherId: createdVoucher.VoucherNumber,
      totalAmount: totalDebit,
      rowsCount: voucherRows.length,
      aiExplanation: aiVoucherData.explanation,
      // Combined information
      attachedFileId: fileId,
      fullVoucherResponse: createdVoucher,
      fullFileResponse: uploadedFile.Archive
    };

    variables[variableName] = combinedResult;

    onLog({
      level: 'info',
      message: `Combined operation completed successfully: File uploaded and voucher ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber} created with attachment`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: combinedResult
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error in combined upload and voucher creation: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
