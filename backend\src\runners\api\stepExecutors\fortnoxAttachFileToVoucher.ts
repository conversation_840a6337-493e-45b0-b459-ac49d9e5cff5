import axios from 'axios';
import type { FortnoxAttachFileToVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * Fortnox Voucher File Connection interface
 */
interface FortnoxVoucherFileConnection {
  FileId: string;
  VoucherNumber: number;
  VoucherSeries: string;
}

/**
 * Fortnox Voucher File Connection API response interface
 */
interface FortnoxVoucherFileConnectionResponse {
  VoucherFileConnection: {
    '@url': string;
    FileId: string;
    VoucherNumber: number;
    VoucherSeries: string;
  };
}

/**
 * Execute Fortnox Attach File to Voucher step
 */
export async function executeFortnoxAttachFileToVoucher(
  step: FortnoxAttachFileToVoucherStep,
  context: FortnoxExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Attach File to Voucher: ${step.id}`,
      stepId: step.id
    });

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    // Get file ID from variable
    const interpolatedFileIdVariable = interpolateVariables(step.fileIdVariable, variables);
    const fileData = variables[interpolatedFileIdVariable];

    let fileId: string;
    if (typeof fileData === 'string') {
      fileId = fileData;
    } else if (fileData && typeof fileData === 'object' && fileData.fileId) {
      fileId = fileData.fileId;
    } else {
      throw new Error(`No valid file ID found in variable: ${interpolatedFileIdVariable}`);
    }

    // Get voucher number from variable
    const interpolatedVoucherNumberVariable = interpolateVariables(step.voucherNumberVariable, variables);
    const voucherData = variables[interpolatedVoucherNumberVariable];

    let voucherNumber: number;
    if (typeof voucherData === 'number') {
      voucherNumber = voucherData;
    } else if (voucherData && typeof voucherData === 'object' && voucherData.voucherNumber) {
      voucherNumber = voucherData.voucherNumber;
    } else if (typeof voucherData === 'string' && !isNaN(parseInt(voucherData))) {
      voucherNumber = parseInt(voucherData);
    } else {
      throw new Error(`No valid voucher number found in variable: ${interpolatedVoucherNumberVariable}`);
    }

    // Get voucher series from variable or use default
    let voucherSeries = 'A'; // Default series
    if (step.voucherSeriesVariable) {
      const interpolatedVoucherSeriesVariable = interpolateVariables(step.voucherSeriesVariable, variables);
      const voucherSeriesData = variables[interpolatedVoucherSeriesVariable];

      if (typeof voucherSeriesData === 'string') {
        voucherSeries = voucherSeriesData;
      } else if (voucherSeriesData && typeof voucherSeriesData === 'object' && voucherSeriesData.voucherSeries) {
        voucherSeries = voucherSeriesData.voucherSeries;
      }
    } else if (voucherData && typeof voucherData === 'object' && voucherData.voucherSeries) {
      voucherSeries = voucherData.voucherSeries;
    }

    onLog({
      level: 'info',
      message: `Attaching file ${fileId} to voucher ${voucherSeries}${voucherNumber}`,
      stepId: step.id
    });

    // Create voucher file connection
    const voucherFileConnection: FortnoxVoucherFileConnection = {
      FileId: fileId,
      VoucherNumber: voucherNumber,
      VoucherSeries: voucherSeries
    };

    // Send attachment request to Fortnox
    let attachmentResponse;
    try {
      attachmentResponse = await axios.post(
        'https://api.fortnox.se/3/voucherfileconnections',
        { VoucherFileConnection: voucherFileConnection },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      // Log detailed error information from Fortnox API
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Voucher File Connection API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      // Also log the connection data that was sent
      onLog({
        level: 'error',
        message: `Voucher file connection data sent to Fortnox: ${JSON.stringify({ VoucherFileConnection: voucherFileConnection }, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Voucher File Connection API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const createdConnection = attachmentResponse.data as FortnoxVoucherFileConnectionResponse;

    // Store attachment information in variables
    const variableName = step.variableName || getDefaultVariableName('fortnoxAttachFileToVoucher', stepIndex);
    const attachmentResult = {
      fileId: createdConnection.VoucherFileConnection.FileId,
      voucherNumber: createdConnection.VoucherFileConnection.VoucherNumber,
      voucherSeries: createdConnection.VoucherFileConnection.VoucherSeries,
      url: createdConnection.VoucherFileConnection['@url'],
      fullResponse: createdConnection.VoucherFileConnection
    };

    variables[variableName] = attachmentResult;

    onLog({
      level: 'info',
      message: `File attached successfully to voucher ${voucherSeries}${voucherNumber}`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: attachmentResult
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error attaching file to voucher: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
